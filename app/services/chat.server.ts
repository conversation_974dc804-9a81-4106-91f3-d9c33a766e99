import { streamText, tool } from 'ai';
import { openai } from './openai.server';
import { tools } from '~/tools';

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface ChatRequest {
  messages: ChatMessage[];
  model?: string;
}

export async function createChatStream(request: ChatRequest): Promise<ReadableStream> {
  const { messages, model } = request;
  console.log('Received messages:', messages, 'Model:', model);

  const stream = new ReadableStream({
    async start(controller) {
      const encoder = new TextEncoder();
      const sendJSON = (data: object) => {
        controller.enqueue(encoder.encode(`data: ${JSON.stringify(data)}\n\n`));
      };

      try {
        const result = streamText({
          model: openai(model || 'gpt-4o-mini'),
          system: "You are a helpful assistant. When you use tools to gather information, you MUST always provide a comprehensive response based on the tool results. After calling a tool and receiving results, you MUST analyze and summarize the information for the user in a clear and helpful way. Never end your response with just a tool call - always follow up with explanatory text.",
          messages,
          tools: tools.reduce((acc, t) => {
            acc[t.name] = tool({
              description: t.description,
              inputSchema: t.schema,
              execute: t.execute,
            });
            return acc;
          }, {} as any),
          toolChoice: 'auto',
        });

        console.log('StreamText result created, starting to process fullStream');

        let hasToolCalls = false;

        for await (const part of result.fullStream) {
          console.log('Processing stream part:', part.type);

          if (part.type === 'tool-call') {
            console.log('Tool call detected:', part.toolName, part.input);
            hasToolCalls = true;
            sendJSON({ type: 'tool_call', name: part.toolName, args: part.input });
          } else if (part.type === 'tool-result') {
            console.log('Tool result received:', part.toolName, 'Output:', part.output);
            // Send tool result to frontend for debugging/transparency
            sendJSON({ type: 'tool_result', name: part.toolName, result: part.output });
          } else if (part.type === 'text-delta') {
            console.log('Text delta received:', part.text);
            sendJSON({ type: 'text_chunk', content: part.text });
          } else if (part.type === 'finish') {
            console.log('Stream finished with reason:', part.finishReason);

            // If we had tool calls but no text was generated, force a follow-up
            if (hasToolCalls && part.finishReason === 'tool-calls') {
              console.log('Tool calls finished without text, generating follow-up...');

              // Create a follow-up request to generate explanatory text
              const followUpResult = streamText({
                model: openai(model || 'gpt-4o-mini'),
                system: "Based on the tool results provided, give a comprehensive and helpful response to the user's question. Analyze and summarize the information clearly.",
                messages: [
                  ...messages,
                  { role: 'assistant', content: 'I have gathered the requested information using tools. Let me provide you with a comprehensive response based on the results.' }
                ],
              });

              for await (const followUpPart of followUpResult.fullStream) {
                if (followUpPart.type === 'text-delta') {
                  console.log('Follow-up text delta:', followUpPart.text);
                  sendJSON({ type: 'text_chunk', content: followUpPart.text });
                } else if (followUpPart.type === 'finish') {
                  console.log('Follow-up finished');
                  break;
                } else if (followUpPart.type === 'error') {
                  console.error('Follow-up error:', followUpPart.error);
                  break;
                }
              }
            }
            break;
          } else if (part.type === 'error') {
            console.error('Stream error:', part.error);
            sendJSON({ type: 'error', message: String(part.error) });
            break;
          } else {
            console.log('Unknown stream part type:', part.type);
          }
        }
      } catch (e) {
        console.error('Chat stream error:', e);
        const errorPayload = { type: 'error', message: (e as Error).message };
        sendJSON(errorPayload);
      } finally {
        sendJSON({ type: 'end' });
        controller.close();
      }
    },
  });

  return stream;
}
