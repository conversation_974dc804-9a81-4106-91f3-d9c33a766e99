import { useState, useEffect, useRef } from "react";


export function ChatDialogue({models}: { models: any[] }) {
  const [messages, setMessages] = useState<any[]>([]);
  const [input, setInput] = useState("");
  const [model, setModel] = useState("gpt-4o-mini");
  const [isLoading, setIsLoading] = useState(false);
  const [toolStatus, setToolStatus] = useState<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, toolStatus]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    console.log('Form submitted');
    if (!input.trim()) return;

    const newMessages = [...messages, { role: 'user', content: input }];
    console.log('Sending messages:', newMessages);
    setMessages(newMessages);
    setInput("");
    setIsLoading(true);
    setToolStatus(null);

    let assistantMessage = { role: 'assistant', content: '' };
    let messageIndex = newMessages.length;

    try {
      console.log('Making fetch request to chat API');
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ messages: newMessages, model }),
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);
      if (!response.body) {
        console.log('No response body');
        return;
      }

      const reader = response.body.pipeThrough(new TextDecoderStream()).getReader();
      console.log('Starting to read response stream...');

      while (true) {
        const { value, done } = await reader.read();
        if (done) {
          console.log('Stream reading completed');
          break;
        }

        console.log('Received chunk:', value);
        const lines = value.split('\n\n').filter(Boolean);
        console.log('Parsed lines:', lines);
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            console.log('Processing data line:', line);
            const data = JSON.parse(line.substring(5));
            console.log('Parsed data:', data);
            if (data.type === 'tool_call') {
              setToolStatus({ name: data.name, args: data.args });
            } else if (data.type === 'text_chunk') {
              if (toolStatus) setToolStatus(null);
              assistantMessage.content += data.content;
              setMessages(prev => {
                const updatedMessages = [...prev];
                if (updatedMessages[messageIndex]?.role === 'assistant') {
                  updatedMessages[messageIndex] = assistantMessage;
                } else {
                  updatedMessages.push(assistantMessage);
                }
                return updatedMessages;
              });
            } else if (data.type === 'end') {
              setIsLoading(false);
              return;
            } else if (data.type === 'error') {
              console.error('Error from server:', data.message);
              setIsLoading(false);
              setToolStatus({ name: 'Error', args: { message: data.message } });
              return;
            }
          }
        }
      }
    } catch (error) {
      console.error("Fetch error:", error);
      setIsLoading(false);
      setToolStatus({ name: 'Error', args: { message: 'Failed to connect to the server.' } });
    }
  };

  return (
    <div style={{ fontFamily: "system-ui, sans-serif", lineHeight: "1.8", padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      <h1>Chat Bot</h1>
      <div style={{ border: "1px solid #ccc", borderRadius: "8px", height: "500px", overflowY: "auto", padding: "10px", display: "flex", flexDirection: "column" }}>
        {messages.map((m, i) => (
          <div key={i} style={{ alignSelf: m.role === 'user' ? 'flex-end' : 'flex-start', background: m.role === 'user' ? '#dcf8c6' : '#f1f0f0', borderRadius: '10px', padding: '8px 12px', margin: '5px', maxWidth: '70%' }}>
            <strong>{m.role === 'user' ? 'You' : 'Assistant'}:</strong> <pre style={{ whiteSpace: 'pre-wrap', margin: 0, fontFamily: 'inherit' }}>{m.content}</pre>
          </div>
        ))}
        {toolStatus && (
          <div style={{ alignSelf: 'flex-start', background: '#e0e0e0', borderRadius: '10px', padding: '8px 12px', margin: '5px', maxWidth: '70%' }}>
            <i>Calling tool: {toolStatus.name}({JSON.stringify(toolStatus.args)})</i>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>
      <form onSubmit={handleSubmit} style={{ display: "flex", marginTop: "10px" }}>
        <input
          type="text"
          name="message"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          disabled={isLoading}
          style={{ flexGrow: 1, padding: "8px", borderRadius: "5px 0 0 5px", border: "1px solid #ccc" }}
        />
        <select
          name="model"
          value={model}
          onChange={(e) => setModel(e.target.value)}
          disabled={isLoading}
          style={{ padding: "8px", border: "1px solid #ccc", borderLeft: "none" }}
        >
          {models.map((m: any) => (
            <option key={m.id} value={m.id} title={m.description}>
              {m.name}
            </option>
          ))}
        </select>
        <button type="submit" disabled={isLoading} style={{ padding: "8px 15px", borderRadius: "0 5px 5px 0", border: "1px solid #ccc", borderLeft: "none", cursor: "pointer" }}>
          {isLoading ? '...' : 'Send'}
        </button>
      </form>
    </div>
  );
};